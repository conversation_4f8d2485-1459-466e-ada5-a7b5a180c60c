package com.example.addon.mixin;

import com.example.addon.modules.Autoclicker;
import com.example.addon.modules.Aimbot;
import com.example.addon.utils.RaycastUtils;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import static meteordevelopment.meteorclient.MeteorClient.mc;

@Mixin(ClientPlayerInteractionManager.class)
public class ClientPlayerInteractionManagerMixin {

    @Inject(method = "attackBlock", at = @At("HEAD"), cancellable = true)
    private void onAttackBlock(BlockPos pos, Direction direction, CallbackInfoReturnable<Boolean> cir) {
        // Get the autoclicker module
        Autoclicker autoclicker = Modules.get().get(Autoclicker.class);

        // Block all block breaking if autoclicker is active and block obstructed attacks is enabled
        if (autoclicker != null && autoclicker.isActive() && autoclicker.shouldBlockObstructedAttacks()) {
            // Cancel all block attacks when this setting is enabled
            cir.setReturnValue(false);

            // Log the blocked attempt (only when debug logging is available)
            if (mc.player != null) {
                
            }
        }
    }
    
    @Inject(method = "attackEntity", at = @At("HEAD"), cancellable = true)
    private void onAttackEntity(PlayerEntity player, Entity target, CallbackInfo ci) {
        // Get the aimbot module
        Aimbot aimbot = Modules.get().get(Aimbot.class);
        
        // Only apply obstruction checking if aimbot is active and obstruction blocking is enabled
        if (aimbot != null && aimbot.isActive() && aimbot.shouldBlockObstructedAttacks() && aimbot.isObstructed()) {
            // Cancel the attack if there's an obstruction
            ci.cancel();
            
            // Log the blocked attempt (only when debug logging is available)
            if (mc.player != null) {
                
            }
        }
    }
}