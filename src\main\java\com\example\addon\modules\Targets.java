package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.EntityTypeListSetting;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.StringListSetting;
import meteordevelopment.meteorclient.settings.BoolSetting;
import meteordevelopment.meteorclient.systems.modules.Module;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.scoreboard.Team;
import net.minecraft.scoreboard.Scoreboard;

import java.util.Set;
import java.util.List;
import java.util.ArrayList;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class Targets extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    public final Setting<Set<EntityType<?>>> entityFilter = sgGeneral.add(new EntityTypeListSetting.Builder()
        .name("entities")
        .description("Which entities to target.")
        .defaultValue(Set.of(EntityType.PLAYER))
        .build()
    );

    public final Setting<Double> range = sgGeneral.add(new DoubleSetting.Builder().name("range").description("How far to check for targets.").defaultValue(4.5).range(2.0, 6.0).sliderRange(2.0, 6.0).build());
    
    public final Setting<List<String>> teammates = sgGeneral.add(new StringListSetting.Builder()
        .name("teammates")
        .description("Player UUIDs to exclude from targeting (teammates).")
        .defaultValue(new ArrayList<>())
        .build()
    );
    
    public final Setting<Boolean> checkScoreboardTeams = sgGeneral.add(new BoolSetting.Builder()
        .name("check-scoreboard-teams")
        .description("Check scoreboard teams to determine teammates.")
        .defaultValue(true)
        .build()
    );

    public Targets() {
        super(AddonTemplate.CATEGORY, "targets", "Allows you to select targets for other modules.");
    }
    
    /**
     * Checks if an entity should be targeted based on the filter settings and teammate list
     * @param entity The entity to check
     * @return true if the entity should be targeted, false otherwise
     */
    public boolean shouldTarget(net.minecraft.entity.Entity entity) {
        // Check if entity type is in the filter
        if (!entityFilter.get().contains(entity.getType())) {
            return false;
        }
        
        // Check if entity is a player and on the teammate list
        if (entity instanceof PlayerEntity) {
            PlayerEntity player = (PlayerEntity) entity;
            // Check if player is in the teammates list
            for (String teammateUuid : teammates.get()) {
                if (player.getUuid().toString().equalsIgnoreCase(teammateUuid)) {
                    return false; // Don't target teammates
                }
            }
            
            // Check scoreboard teams if enabled
            if (checkScoreboardTeams.get() && mc.player != null) {
                Scoreboard scoreboard = mc.world.getScoreboard();
                
                // Get the team of the local player
                Team localPlayerTeam = scoreboard.getScoreHolderTeam(mc.player.getUuid().toString());
                
                // Get the team of the target player
                Team targetPlayerTeam = scoreboard.getScoreHolderTeam(player.getUuid().toString());
                
                // If both players are on the same team, don't target
                if (localPlayerTeam != null && targetPlayerTeam != null && localPlayerTeam.equals(targetPlayerTeam)) {
                    return false; // Don't target teammates from scoreboard
                }
            }
        }
        
        return true;
    }
}