package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.EnchantmentUtils;
import com.example.addon.utils.MotionUtils;
import com.example.addon.utils.SmoothAimingUtils;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.utils.entity.TargetUtils;
import meteordevelopment.meteorclient.utils.entity.SortPriority;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.item.AxeItem;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.Hand;

import java.util.function.Predicate;

import java.lang.reflect.Method;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class StunSlam extends Module {
    private enum State {
        IDLE, AXE_ATTACK_PENDING, AXE_ATTACKED, AXE_SPAM, MACE_SWAPPED, MACE_ATTACKED
    }

    private enum AttackMode {
        Packet,
        KeyPress,
        LeftClick
    }

    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgAiming = settings.createGroup("Aiming");

    private final Setting<AttackMode> attackMode = sgGeneral.add(new EnumSetting.Builder<AttackMode>()
        .name("attack-mode")
        .description("The method used to attack the target.")
        .defaultValue(AttackMode.LeftClick)
        .build()
    );

    private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder().name("min-fall-distance").description("The minimum distance you must be falling for the combo to activate.").defaultValue(1.5).range(0.5, 10.0).sliderRange(0.5, 10.0).build());
    private final Setting<Boolean> requireShield = sgGeneral.add(new BoolSetting.Builder().name("require-shield").description("Only attack players that are actively blocking with a shield.").defaultValue(true).build());
    private final Setting<Boolean> timedMode = sgGeneral.add(new BoolSetting.Builder().name("timed-mode").description("Use timed attacks instead of checking if the shield is down.").defaultValue(false).build());
    private final Setting<Integer> axeAttacks = sgGeneral.add(new IntSetting.Builder().name("axe-attacks").description("Number of axe attacks in timed mode.").defaultValue(3).range(1, 10).sliderRange(1, 10).visible(timedMode::get).build());
    private final Setting<Integer> maxAxeHits = sgGeneral.add(new IntSetting.Builder().name("max-axe-hits").description("Maximum axe attacks before switching to mace (prevents infinite axe spam).").defaultValue(2).range(1, 5).sliderRange(1, 5).visible(() -> !timedMode.get()).build());
    private final Setting<Boolean> silentAim = sgAiming.add(new BoolSetting.Builder().name("silent-aim").description("Aims on the server without moving your camera, with F5 head turning and movement correction.").defaultValue(true).build());
    private final Setting<Double> aimSpeed = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-speed")
        .description("The speed at which the player aims at the target.")
        .defaultValue(1.0d)
        .range(0.1d, 1.0d)
        .build()
    );

    private final Setting<Double> aimRange = sgAiming.add(new DoubleSetting.Builder()
        .name("aim-range")
        .description("The maximum range at which to aim at a target.")
        .defaultValue(4.0)
        .range(0.0, 50.0)
        .sliderRange(0.0, 50.0)
        .build()
    );

    private final Setting<Double> fov = sgAiming.add(new DoubleSetting.Builder()
        .name("fov")
        .description("The field of view (in degrees) within which to target entities.")
        .defaultValue(90.0)
        .min(0.0)
        .max(360.0)
        .sliderMin(0.0)
        .sliderMax(360.0)
        .build()
    );

    private final Setting<Double> exponentialSmoothing = sgAiming.add(new DoubleSetting.Builder()
        .name("exponential-smoothing")
        .description("Exponential smoothing factor for aiming (0.0 = no smoothing, 1.0 = maximum smoothing).")
        .defaultValue(0.5)
        .range(0.0, 1.0)
        .sliderRange(0.0, 1.0)
        .visible(silentAim::get)
        .build());

    private final Setting<Boolean> dontLookUp = sgAiming.add(new BoolSetting.Builder()
        .name("dont-look-up")
        .description("Prevents aiming upward when targeting entities (locks pitch at 0 degrees).")
        .defaultValue(false)
        .build());

    private final Setting<Boolean> fallPredictionAttack = sgGeneral.add(new BoolSetting.Builder()
        .name("fall-prediction-attack")
        .description("Enables attack prediction based on fall time and landing position.")
        .defaultValue(false)
        .build());

    private final Setting<Double> attackRange = sgGeneral.add(new DoubleSetting.Builder()
        .name("attack-range")
        .description("The maximum range at which to attack a target.")
        .defaultValue(3.0)
        .range(0.0, 6.0)
        .sliderRange(0.0, 6.0)
        .build()
    );
    
    private final Setting<Boolean> keepTarget = sgGeneral.add(new BoolSetting.Builder()
        .name("keep-target")
        .description("Keep the same target until they die or move too far away.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> keepTargetDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("keep-target-distance")
        .description("Maximum distance to keep the target.")
        .defaultValue(10.0)
        .min(5.0)
        .sliderMax(20.0)
        .visible(keepTarget::get)
        .build()
    );

    private LivingEntity target = null;
    private int originalSlot = -1;
    private int actionTimer = 0; 
    private State currentState = State.IDLE;
    private Method doAttackMethod;
    private float serverYaw, serverPitch;
    
    private boolean shouldAim = false; 
    
    private int attackTimer = 0;
    private boolean isAttackingMace = false;
    private boolean hasSwappedToAxe = false;
    private int axeAttackCount = 0;
    
    private boolean perFrameTargetInRange = false; 
    private long lastSlamTime = 0;
    private boolean shouldAttackNextTick = false;

    public long lastSlamTime() {
        return lastSlamTime;
    }

    private void performLeftClick() {
        if (mc.options.attackKey != null) {
            KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
        }
    }

    private static Class<?> cachedMaceAuraClass;
    private static java.lang.reflect.Field cachedActionTakenField;
    private static boolean reflectionInitialized = false;

    private int tickCounter = 0;

    public StunSlam() {
        super(AddonTemplate.CATEGORY, "stun-slam", "Performs a frame-perfect, precise combo on shielded players while falling.");
        initializeAttackMethod();
        initializeReflectionCache();
    }

    private static void initializeReflectionCache() {
        if (!reflectionInitialized) {
            try {
                cachedMaceAuraClass = Class.forName("com.example.addon.modules.MaceAura");
                cachedActionTakenField = cachedMaceAuraClass.getDeclaredField("actionTakenThisTick");
                cachedActionTakenField.setAccessible(true);
                reflectionInitialized = true;
            } catch (Exception e) {
                reflectionInitialized = false;
            }
        }
    }

    private void initializeAttackMethod() {
        try {
            try {
                doAttackMethod = mc.getClass().getDeclaredMethod("method_1536");
            } catch (NoSuchMethodException e1) {
                try {
                    doAttackMethod = mc.getClass().getDeclaredMethod("doAttack");
                } catch (NoSuchMethodException e2) {
                    error("Failed to find Minecraft's attack method via reflection. " +
                        "Module will use alternative attack method.");
                    return;
                }
            }
            doAttackMethod.setAccessible(true);
            info("Successfully found attack method via reflection for StunSlam.");
        } catch (Exception e) {
            error("StunSlam: Failed to initialize attack method: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onActivate() {
        reset();
        if (!hasMaceInHotbar()) {
            warning("No mace found in hotbar. Deactivating StunSlam.");
            toggle();
        }
    }
    @Override
    public void onDeactivate() {
        if (originalSlot != -1 && mc.player != null) InvUtils.swap(originalSlot, false);
        reset();
    }

    private void reset() {
        target = null;
        originalSlot = -1;
        actionTimer = 0;
        currentState = State.IDLE;
        shouldAim = false; 
        attackTimer = 0;
        isAttackingMace = false;
        hasSwappedToAxe = false;
        axeAttackCount = 0;
        perFrameTargetInRange = false;
        shouldAttackNextTick = false;
    }

    private boolean isHoldingAxe() {
        if (mc.player == null) return false;
        return mc.player.getMainHandStack().getItem() instanceof AxeItem;
    }

    private boolean hasMaceInHotbar() {
        for (int i = 0; i < 9; i++) {
            ItemStack stack = mc.player.getInventory().getStack(i);
            if (stack.getItem() == Items.MACE && hasDensityEnchantment(stack)) {
                return true;
            }
        }
        return false;
    }

    private boolean hasDensityEnchantment(ItemStack stack) {
        return EnchantmentUtils.hasEnchantment(stack, Enchantments.DENSITY);
    }

    private double getEuclideanDistance(LivingEntity target) {
        if (mc.player == null || target == null) return Double.MAX_VALUE;
        Vec3d playerPos = mc.player.getEyePos(); 
        Vec3d targetPos = target.getEyePos();
        return playerPos.distanceTo(targetPos);
    }

    private boolean checkTargetInRange(LivingEntity targetEntity) { 
        if (targetEntity == null || mc.player == null) return false;
        return getEuclideanDistance(targetEntity) <= attackRange.get();
    }

    private void attackTarget() {
        if (doAttackMethod == null) {
            initializeAttackMethod();
            
            if (doAttackMethod == null) {
                error("StunSlam: Attack method is null. Using alternative attack method.");
                if (mc.options.attackKey != null) {
                    KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
                } else {
                    error("KeyPress mode enabled, but 'attackKey' is null. This should not happen.");
                }
                return;
            }
        }

        if (mc.options.attackKey != null) {
            KeyBinding.onKeyPressed(mc.options.attackKey.getDefaultKey());
        } else {
            error("KeyPress mode enabled, but 'attackKey' is null. This should not happen.");
        }
        
        if (attackMode.get() == AttackMode.LeftClick) {
            performLeftClick();
        } else {
            try {
                doAttackMethod.invoke(mc);
            } catch (Exception e) {
                error("StunSlam: Failed to invoke attack method: " + e.getMessage());
                if (mc.interactionManager != null && target != null) {
                    mc.interactionManager.attackEntity(mc.player, target);
                    mc.player.swingHand(Hand.MAIN_HAND);
                }
            }
        }
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) { reset(); return; }

        tickCounter++;

        if (hasAnotherModuleTakenAction()) {
            log("Another module has already taken action this tick. Skipping StunSlam.");
            return;
        }

        if (mc.player.isOnGround() && currentState != State.IDLE) {
            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
            reset();
            return;
        }

        Targets targetsModule = Modules.get().get(Targets.class);

        perFrameTargetInRange = (target != null && checkTargetInRange(target));

        // Fall Prediction Attack Logic
        if (fallPredictionAttack.get() && target != null && mc.player.fallDistance > 0) {
            double fallTime = MotionUtils.calculateFallTime(mc.player, target.getY(), attackRange.get());
            // Convert fallTime from seconds to milliseconds
            double fallTimeMs = fallTime * 1000;

            // Check if fall time is less than 50ms and greater than 0, and target is in range
            if (fallTimeMs > 0 && fallTimeMs < 50 && checkTargetInRange(target)) {
                shouldAttackNextTick = true;
                info("Fall Prediction: Target in range and will land in < 50ms. Fall Time: " + String.format("%.2f", fallTimeMs) + "ms. Queuing attack.");
            }
        }

        if (shouldAttackNextTick) {
            if (currentState == State.IDLE) {
                // Attempt to initiate the attack sequence
                if (!hasSwappedToAxe) {
                    if (findValidTargetWithKeepTarget(targetsModule)) {
                        if (!isHoldingAxe()) {
                            FindItemResult axeResult = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof AxeItem);
                            if (axeResult.found()) {
                                originalSlot = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot();
                                InvUtils.swap(axeResult.slot(), false);
                                hasSwappedToAxe = true;
                            } else {
                                warning("No axe found in hotbar for fall prediction combo.");
                                reset();
                                return;
                            }
                        } else {
                            hasSwappedToAxe = true;
                        }
                    }
                }

                if (target != null && hasSwappedToAxe) {
                    if (perFrameTargetInRange) {
                        FindItemResult slotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                        if (!slotResult.found()) { reset(); return; }
                        originalSlot = slotResult.slot();
                        currentState = State.AXE_ATTACK_PENDING;
                        actionTimer = 0;
                        shouldAttackNextTick = false; // Reset the flag after initiating
                    }
                }
            } else if (currentState == State.AXE_ATTACK_PENDING || currentState == State.AXE_SPAM) {
                // Continue with the attack sequence if already in progress
                // No need to reset shouldAttackNextTick here, as the state machine will handle it
            } else {
                // If in another state, reset the flag as we might have missed the window or are in an unexpected state
                shouldAttackNextTick = false;
            }
        }

        if (currentState == State.IDLE) {
            if (!hasSwappedToAxe) {
                if (findValidTargetWithKeepTarget(targetsModule)) {
                    if (!isHoldingAxe()) {
                        FindItemResult axeResult = InvUtils.findInHotbar(itemStack -> itemStack.getItem() instanceof AxeItem);
                        if (axeResult.found()) {
                            originalSlot = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem()).slot();
                            InvUtils.swap(axeResult.slot(), false);
                            hasSwappedToAxe = true;
                        } else {
                            warning("No axe found in hotbar to start StunSlam combo.");
                            reset();
                            return;
                        }
                    } else {
                        hasSwappedToAxe = true;
                    }
                }
            }
            
            if (target != null && hasSwappedToAxe && mc.player.fallDistance >= minFallDistance.get()) {
                if (perFrameTargetInRange) {
                    FindItemResult slotResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
                    if (!slotResult.found()) { reset(); return; }
                    originalSlot = slotResult.slot();
                    currentState = State.AXE_ATTACK_PENDING;
                    actionTimer = 0;
                }
            }
        }

        if (target == null || tickCounter % 3 == 0 || (keepTarget.get() && target != null && !target.isAlive())) {
             findValidTargetWithKeepTarget(targetsModule);
        }

        if (target != null && shouldAim && silentAim.get() && !mc.player.isOnGround()) {
             applyMovementCorrection();
             mc.player.networkHandler.sendPacket(new PlayerMoveC2SPacket.LookAndOnGround(serverYaw, serverPitch, mc.player.isOnGround(), false));
        }

        if (targetsModule == null || !targetsModule.isActive()) {
            if (currentState != State.IDLE) {
                if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                reset();
            }
            return;
        }

        if (currentState != State.IDLE && (target == null || !target.isAlive() || getEuclideanDistance(target) > targetsModule.range.get() + 1)) {
            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
            reset();
            return;
        }
        
        if (mc.player.fallDistance < minFallDistance.get() && currentState != State.IDLE) {
            if (originalSlot != -1) InvUtils.swap(originalSlot, false);
            reset();
            return;
        }

        if (target != null && perFrameTargetInRange && mc.player.fallDistance >= minFallDistance.get() && !isPlayerFlyingWithElytra()) {
            
            actionTimer++;
            
            switch (currentState) {
                case AXE_ATTACK_PENDING:
                if (actionTimer == 1) {
                    attackTarget();
                    axeAttackCount++;
                    
                    info("StunSlam: TICK " + actionTimer + " - Axe attack #" + axeAttackCount);
                    
                    if (timedMode.get() && axeAttackCount < axeAttacks.get()) {
                        currentState = State.AXE_SPAM;
                        actionTimer = 0;
                    } else if (!timedMode.get() && target.isBlocking() && requireShield.get() && axeAttackCount < maxAxeHits.get()) {
                        currentState = State.AXE_SPAM;
                        actionTimer = 0;
                    } else {
                        currentState = State.AXE_ATTACKED;
                        actionTimer = 0; // This was missing!
                    }
                }
                break;
                    
                case AXE_SPAM:
                    if (actionTimer == 1) {
                        if (timedMode.get() && axeAttackCount < axeAttacks.get()) {
                            attackTarget();
                            axeAttackCount++;
                            info("StunSlam: TICK " + (actionTimer + axeAttackCount - 1) + " - Timed axe attack #" + axeAttackCount);
                            
                            if (axeAttackCount >= axeAttacks.get()) {
                                currentState = State.AXE_ATTACKED;
                                actionTimer = 0;
                            } else {
                                actionTimer = 0;
                            }
                        } else if (!timedMode.get() && target.isBlocking() && requireShield.get() && axeAttackCount < maxAxeHits.get()) {
                            attackTarget();
                            axeAttackCount++;
                            info("StunSlam: TICK " + (actionTimer + axeAttackCount - 1) + " - Shield-break axe attack #" + axeAttackCount);
                            
                            if (!target.isBlocking() || axeAttackCount >= maxAxeHits.get()) {
                                currentState = State.AXE_ATTACKED;
                                actionTimer = 0;
                            } else {
                                actionTimer = 0;
                            }
                        } else {
                            currentState = State.AXE_ATTACKED;
                            actionTimer = 0;
                        }
                    }
                    break;
                    
                case AXE_ATTACKED:
                if (actionTimer == 1) {
                    FindItemResult maceResult = InvUtils.findInHotbar(s -> s.getItem() == Items.MACE && hasDensityEnchantment(s));
                    if (!maceResult.found()) {
                        warning("No Density Mace found in hotbar, resetting StunSlam.");
                        if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                        reset(); 
                        return;
                    }
                    InvUtils.swap(maceResult.slot(), false);
                    currentState = State.MACE_SWAPPED;
                    info("StunSlam: TICK " + (actionTimer + axeAttackCount) + " - Swapped to mace");
                }
                break;
                    
                case MACE_SWAPPED:
                    if (actionTimer == 2) {
                        if (mc.player.getMainHandStack().getItem() == Items.MACE) {
                            attackTarget();
                            currentState = State.MACE_ATTACKED;
                            isAttackingMace = true;
                            lastSlamTime = System.currentTimeMillis();
                            info("StunSlam: TICK " + (actionTimer + axeAttackCount) + " - Mace attack executed!");
                            
                            if (reflectionInitialized && cachedActionTakenField != null) {
                                try {
                                    cachedActionTakenField.setBoolean(null, true);
                                } catch (Exception e) {
                                    // Handle or ignore
                                }
                            }
                        }
                    }
                    break;
                    
                case MACE_ATTACKED:
                    if (actionTimer == 3) {
                        if (originalSlot != -1) InvUtils.swap(originalSlot, false);
                        info("StunSlam: TICK " + (actionTimer + axeAttackCount) + " - Combo complete, swapped back to original item");
                        reset();
                    }
                    break;
            }
        }
    }

    private boolean findValidTargetWithKeepTarget(Targets targetsModule) {
        if (targetsModule == null || !targetsModule.isActive()) {
            this.target = null;
            return false;
        }

        if (keepTarget.get() && this.target != null) {
            if (this.target.isAlive() && mc.player.distanceTo(this.target) <= keepTargetDistance.get()) {
                if (fov.get() >= 360.0 || isWithinFOV(this.target)) {
                    return true;
                } else {
                    this.target = null;
                }
            } else {
                this.target = null;
            }
        }

        if (this.target == null) {
            Predicate<Entity> targetPredicate = entity -> {
                if (entity.equals(mc.player)) return false;
                
                if (targetsModule == null || !targetsModule.isActive()) {
                    return false;
                }
                
                return targetsModule.shouldTarget(entity);
            };

            Entity foundTarget = TargetUtils.get(targetPredicate, SortPriority.LowestDistance);

            if (foundTarget instanceof LivingEntity) {
                LivingEntity livingTarget = (LivingEntity) foundTarget;
                if (getEuclideanDistance(livingTarget) <= aimRange.get()) {
                    if (fov.get() >= 360.0 || isWithinFOV(livingTarget)) {
                        this.target = livingTarget;
                        return true;
                    }
                }
            }
            this.target = null;
        }

        return this.target != null;
    }

    private boolean isWithinFOV(LivingEntity entity) {
        if (mc.player == null || entity == null) return false;

        double fovValue = fov.get();
        if (fovValue >= 360.0) return true;

        Vec3d playerPos = mc.player.getEyePos();
        Vec3d targetPos = entity.getEyePos();

        double deltaX = targetPos.x - playerPos.x;
        double deltaY = targetPos.y - playerPos.y;
        double deltaZ = targetPos.z - playerPos.z;

        double distanceXZSquared = deltaX * deltaX + deltaZ * deltaZ;

        if (distanceXZSquared < 0.01) return true;

        double distanceXZ = Math.sqrt(distanceXZSquared);

        float targetYaw = (float) Math.toDegrees(Math.atan2(deltaZ, deltaX)) - 90.0F;
        float targetPitch = (float) Math.toDegrees(-Math.atan2(deltaY, distanceXZ));

        targetPitch = MathHelper.clamp(targetPitch, -90.0F, 90.0F);

        if (dontLookUp.get()) {
            targetPitch = Math.min(targetPitch, 0.0F);
        }

        float currentYaw = mc.player.getYaw();
        float currentPitch = mc.player.getPitch();

        float yawDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(currentYaw, targetYaw));
        float pitchDiff = Math.abs(SmoothAimingUtils.getShortestAngleDifference(currentPitch, targetPitch));

        double halfFov = fovValue / 2.0;
        return yawDiff <= halfFov && pitchDiff <= halfFov;
    }
    
    @EventHandler
    private void onRender3d(Render3DEvent event) {
        if (mc.player == null || mc.world == null) return;

        if (target != null && currentState != State.IDLE && !isPlayerFlyingWithElytra()) {
            shouldAim = (getEuclideanDistance(target) <= aimRange.get() && (fov.get() >= 360.0 || isWithinFOV(target)));

            if (shouldAim) {
                Vec3d playerPos = mc.player.getEyePos();
                Vec3d targetPos = target.getEyePos();

                float[] rotations;
                if (exponentialSmoothing.get() > 0.0) {
                    rotations = SmoothAimingUtils.calculateExponentialSmoothRotations(
                        mc.player.getYaw(),
                        mc.player.getPitch(),
                        targetPos,
                        playerPos,
                        aimSpeed.get() * 25.0,
                        (float) event.frameTime,
                        exponentialSmoothing.get()
                    );
                } else {
                    rotations = SmoothAimingUtils.calculateSmoothRotations(
                        mc.player.getYaw(),
                        mc.player.getPitch(),
                        targetPos,
                        playerPos,
                        aimSpeed.get() * 25.0,
                        (float) event.frameTime,
                        0.0
                    );
                }

                serverYaw = rotations[0];
                serverPitch = rotations[1];

                if (silentAim.get()) {
                    mc.player.setHeadYaw(serverYaw);
                    mc.player.setBodyYaw(serverYaw);
                } else {
                    mc.player.setYaw(serverYaw);
                    mc.player.setPitch(serverPitch);
                }
            }
        } else {
            shouldAim = false;
        }
    }

    private void applyMovementCorrection() {
        float forward = (float) mc.player.input.getMovementInput().y;
        float strafe = (float) mc.player.input.getMovementInput().x;

        if (forward == 0.0f && strafe == 0.0f) return;
        float clientYaw = mc.player.getYaw();
        float yawDiff = SmoothAimingUtils.getShortestAngleDifference(clientYaw, serverYaw);
        Vec3d moveInput = new Vec3d(strafe, 0, forward).normalize();
        Vec3d correctedVec = moveInput.rotateY((float) -Math.toRadians(yawDiff));
        float speed = (float) (mc.player.getMovementSpeed() * (forward != 0.0f && strafe != 0.0f ? 0.7071 : 1.0));
        mc.player.setVelocity(correctedVec.x * speed, mc.player.getVelocity().y, correctedVec.z * speed);
    }

    public boolean isSlamming() {
        return currentState != State.IDLE;
    }

    public boolean isTargetWithinRange() {
        return perFrameTargetInRange; 
    }

    private boolean hasAnotherModuleTakenAction() {
        if (reflectionInitialized && cachedActionTakenField != null) {
            try {
                return cachedActionTakenField.getBoolean(null);
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }
    
    private void log(String message) {
        info(message);
    }

    private boolean isPlayerFlyingWithElytra() {
        if (mc.player == null) return false;
        ItemStack chestSlot = mc.player.getInventory().getStack(38);
        return chestSlot.getItem() == Items.ELYTRA &&
               !mc.player.isOnGround() &&
               mc.player.getVelocity().y > -0.5;
    }
}