package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import com.example.addon.utils.InputCheckUtils;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.AxeItem;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;

import static meteordevelopment.meteorclient.MeteorClient.mc;

public class DoubleClickSwap extends Module {
    private enum SwapState {
        IDLE,
        FIRST_CLICK_DETECTED,
        ELYTRA_EQUIPPED,
        AXE_EQUIPPED,
        MACE_EQUIPPED,
        COOLDOWN
    }

    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    private final Setting<Integer> doubleClickWindow = sgGeneral.add(new IntSetting.Builder()
        .name("double-click-window")
        .description("Time window in milliseconds to detect double-click.")
        .defaultValue(300)
        .range(100, 1000)
        .sliderRange(100, 1000)
        .build()
    );

    private final Setting<Integer> swapDelay = sgGeneral.add(new IntSetting.Builder()
        .name("swap-delay")
        .description("Delay in ticks between each swap.")
        .defaultValue(2)
        .range(1, 10)
        .sliderRange(1, 10)
        .build()
    );

    private final Setting<Integer> equipDelay = sgGeneral.add(new IntSetting.Builder()
        .name("equip-delay")
        .description("Delay in ticks for equipping elytra/chestplate.")
        .defaultValue(3)
        .range(1, 10)
        .sliderRange(1, 10)
        .build()
    );

    private final Setting<Integer> holdDuration = sgGeneral.add(new IntSetting.Builder()
        .name("hold-duration")
        .description("How long to hold each item in ticks.")
        .defaultValue(5)
        .range(1, 20)
        .sliderRange(1, 20)
        .build()
    );

    private final Setting<Boolean> requireShield = sgGeneral.add(new BoolSetting.Builder()
        .name("require-shield")
        .description("Only activate when targeting a player with a shield.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("debug-mode")
        .description("Show debug messages.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> autoAttack = sgGeneral.add(new BoolSetting.Builder()
        .name("auto-attack")
        .description("Automatically attack with axe and mace during the sequence.")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> onlyWhileFalling = sgGeneral.add(new BoolSetting.Builder()
        .name("only-while-falling")
        .description("Only activate the sequence while falling.")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> minFallDistance = sgGeneral.add(new DoubleSetting.Builder()
        .name("min-fall-distance")
        .description("Minimum fall distance required to activate.")
        .defaultValue(1.0)
        .range(0.0, 10.0)
        .sliderRange(0.0, 10.0)
        .visible(onlyWhileFalling::get)
        .build()
    );

    private SwapState currentState = SwapState.IDLE;
    private long firstClickTime = 0;
    private long lastRightClickTime = 0;
    private boolean wasRightClickPressed = false;
    private int stateTimer = 0;
    private int originalSlot = -1;
    private int elytraSlot = -1;
    private int axeSlot = -1;
    private int maceSlot = -1;

    public DoubleClickSwap() {
        super(AddonTemplate.CATEGORY, "double-click-swap", "Double-click to swap elytra->axe->mace for shield breaking.");
    }

    @Override
    public void onActivate() {
        reset();
        if (debugMode.get()) info("DoubleClickSwap activated");
    }

    @Override
    public void onDeactivate() {
        if (originalSlot != -1 && mc.player != null) {
            InvUtils.swap(originalSlot, false);
        }
        reset();
        if (debugMode.get()) info("DoubleClickSwap deactivated");
    }

    private void reset() {
        currentState = SwapState.IDLE;
        firstClickTime = 0;
        lastRightClickTime = 0;
        wasRightClickPressed = false;
        stateTimer = 0;
        originalSlot = -1;
        elytraSlot = -1;
        axeSlot = -1;
        maceSlot = -1;
    }

    private boolean findRequiredItems() {
        // Find elytra or chestplate
        FindItemResult elytraResult = InvUtils.findInHotbar(stack -> 
            stack.getItem() == Items.ELYTRA || stack.getItem() == Items.NETHERITE_CHESTPLATE ||
            stack.getItem() == Items.DIAMOND_CHESTPLATE || stack.getItem() == Items.IRON_CHESTPLATE ||
            stack.getItem() == Items.GOLDEN_CHESTPLATE || stack.getItem() == Items.CHAINMAIL_CHESTPLATE ||
            stack.getItem() == Items.LEATHER_CHESTPLATE
        );
        
        // Find axe
        FindItemResult axeResult = InvUtils.findInHotbar(stack -> stack.getItem() instanceof AxeItem);
        
        // Find mace
        FindItemResult maceResult = InvUtils.findInHotbar(stack -> stack.getItem() == Items.MACE);

        if (!elytraResult.found()) {
            if (debugMode.get()) warning("No elytra or chestplate found in hotbar");
            return false;
        }
        
        if (!axeResult.found()) {
            if (debugMode.get()) warning("No axe found in hotbar");
            return false;
        }
        
        if (!maceResult.found()) {
            if (debugMode.get()) warning("No mace found in hotbar");
            return false;
        }

        elytraSlot = elytraResult.slot();
        axeSlot = axeResult.slot();
        maceSlot = maceResult.slot();
        
        return true;
    }

    @EventHandler
    private void onTick(TickEvent.Pre event) {
        if (mc.player == null || mc.world == null) {
            reset();
            return;
        }

        boolean isRightClickPressed = InputCheckUtils.isRMBHeld();
        long currentTime = System.currentTimeMillis();

        // Detect right-click press (edge detection)
        if (isRightClickPressed && !wasRightClickPressed) {
            handleRightClickPress(currentTime);
        }
        
        wasRightClickPressed = isRightClickPressed;

        // Handle state machine
        handleStateMachine();
    }

    private void handleRightClickPress(long currentTime) {
        if (currentState == SwapState.IDLE) {
            // First click detected
            firstClickTime = currentTime;
            currentState = SwapState.FIRST_CLICK_DETECTED;
            stateTimer = 0;
            if (debugMode.get()) info("First right-click detected");
            
        } else if (currentState == SwapState.FIRST_CLICK_DETECTED) {
            // Check if this is within the double-click window
            long timeDiff = currentTime - firstClickTime;
            if (timeDiff <= doubleClickWindow.get()) {
                // Double-click detected!
                if (debugMode.get()) info("Double-click detected! Starting swap sequence");
                startSwapSequence();
            } else {
                // Too late, reset to first click
                firstClickTime = currentTime;
                stateTimer = 0;
                if (debugMode.get()) info("Double-click window expired, treating as new first click");
            }
        }
    }

    private void startSwapSequence() {
        // Check if we should only activate while falling
        if (onlyWhileFalling.get() && mc.player.fallDistance < minFallDistance.get()) {
            if (debugMode.get()) info("Not falling enough to activate sequence");
            reset();
            return;
        }

        // Check if we're targeting a shielded player (if required)
        if (requireShield.get() && !isTargetingShieldedPlayer()) {
            if (debugMode.get()) info("Not targeting a shielded player");
            reset();
            return;
        }

        if (!findRequiredItems()) {
            reset();
            return;
        }

        // Store original slot
        FindItemResult originalResult = InvUtils.findInHotbar(mc.player.getMainHandStack().getItem());
        originalSlot = originalResult.found() ? originalResult.slot() : -1;

        // Start with elytra swap
        currentState = SwapState.ELYTRA_EQUIPPED;
        stateTimer = 0;

        // Swap to elytra/chestplate
        InvUtils.swap(elytraSlot, false);
        if (debugMode.get()) info("Swapped to elytra/chestplate");
    }

    private boolean isTargetingShieldedPlayer() {
        if (mc.crosshairTarget == null || mc.crosshairTarget.getType() != HitResult.Type.ENTITY) {
            return false;
        }

        EntityHitResult entityHit = (EntityHitResult) mc.crosshairTarget;
        Entity target = entityHit.getEntity();

        if (!(target instanceof PlayerEntity)) {
            return false;
        }

        PlayerEntity player = (PlayerEntity) target;
        return player.isBlocking();
    }

    private void performAttack() {
        if (mc.crosshairTarget != null && mc.crosshairTarget.getType() == HitResult.Type.ENTITY) {
            EntityHitResult entityHit = (EntityHitResult) mc.crosshairTarget;
            Entity target = entityHit.getEntity();

            if (target instanceof LivingEntity) {
                mc.interactionManager.attackEntity(mc.player, target);
                mc.player.swingHand(Hand.MAIN_HAND);
                if (debugMode.get()) info("Attacked target");
            }
        }
    }

    private void handleStateMachine() {
        stateTimer++;

        switch (currentState) {
            case FIRST_CLICK_DETECTED:
                // Wait for potential second click or timeout
                if (stateTimer > (doubleClickWindow.get() / 50)) { // Convert ms to ticks (50ms per tick)
                    reset();
                }
                break;

            case ELYTRA_EQUIPPED:
                if (stateTimer >= equipDelay.get()) {
                    // Equip the elytra/chestplate
                    mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
                    currentState = SwapState.AXE_EQUIPPED;
                    stateTimer = 0;
                    
                    // Swap to axe
                    InvUtils.swap(axeSlot, false);
                    if (debugMode.get()) info("Equipped elytra/chestplate, swapped to axe");
                }
                break;

            case AXE_EQUIPPED:
                // Auto-attack with axe if enabled
                if (autoAttack.get() && stateTimer == 1) {
                    performAttack();
                }

                if (stateTimer >= holdDuration.get()) {
                    // Hold axe for specified duration, then swap to mace
                    currentState = SwapState.MACE_EQUIPPED;
                    stateTimer = 0;

                    // Swap to mace
                    InvUtils.swap(maceSlot, false);
                    if (debugMode.get()) info("Swapped to mace");
                }
                break;

            case MACE_EQUIPPED:
                // Auto-attack with mace if enabled
                if (autoAttack.get() && stateTimer == 1) {
                    performAttack();
                }

                if (stateTimer >= holdDuration.get()) {
                    // Hold mace for specified duration, then return to original
                    currentState = SwapState.COOLDOWN;
                    stateTimer = 0;

                    // Swap back to original item
                    if (originalSlot != -1) {
                        InvUtils.swap(originalSlot, false);
                        if (debugMode.get()) info("Swapped back to original item");
                    }
                }
                break;

            case COOLDOWN:
                if (stateTimer >= swapDelay.get()) {
                    // Sequence complete
                    reset();
                    if (debugMode.get()) info("Swap sequence complete");
                }
                break;
        }
    }
}
